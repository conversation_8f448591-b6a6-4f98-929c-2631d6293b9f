package com.stanly.drex.wallet.service.impl;

import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.drex.customer.api.response.PassportConnectDTO;
import com.drex.customer.api.response.PassportDTO;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.wallet.dto.WalletBindingQueryRequest;
import com.stanly.drex.wallet.dto.WalletBindingQueryResponse;
import com.stanly.drex.wallet.dto.WalletUnbindRequest;
import com.stanly.drex.wallet.service.WalletBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 钱包绑定服务实现类
 * <p>
 * 注意：需要确保 RemotePassportService 接口中包含以下方法：
 * - Response<WalletBindingQueryResponse> queryWalletBinding(WalletBindingQueryRequest request)
 * - Response<Boolean> unbindWallet(WalletUnbindRequest request)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WalletBindingServiceImpl implements WalletBindingService {

    @DubboReference
    private RemotePassportService remotePassportService;

    @Override
    public WalletBindingQueryResponse queryWalletBinding(WalletBindingQueryRequest request) {
        log.info("查询钱包绑定关系，请求参数: {}", request);
        WalletBindingQueryResponse walletBindingQueryResponse = new WalletBindingQueryResponse();

        try {
            // 调用远程服务查询钱包绑定关系
            Response<PassportDTO> response = remotePassportService.getByHandleName(request.getHandleName());
            if (response.isSuccess() && Objects.nonNull(response.getData())) {
                PassportDTO passportDTO = response.getData();
                String passportId = passportDTO.getPassportId();
                Response<List<PassportConnectDTO>> passportConnect = remotePassportService.getPassportConnect(passportId);
                //可展示所有的绑定钱包，如果request中有walletAddress，则需要过滤出该钱包地址的绑定关系
                if (Objects.nonNull(request.getWalletAddress())) {
                    if (passportConnect.getData() != null && !passportConnect.getData().isEmpty()) {
                        List<PassportConnectDTO> connectList = passportConnect.getData();
                        connectList.removeIf(item -> !item.getWalletAddress().equals(request.getWalletAddress()));
                        connectList.removeIf(item -> !item.getStatus().equals("active"));
                        if (!connectList.isEmpty()) {
                            walletBindingQueryResponse.setBindings(connectList.get(0));
                        }
                    }
                }
                return walletBindingQueryResponse;
            } else {
                log.warn("查询钱包绑定关系失败，响应: {}", response);
                return new WalletBindingQueryResponse();
            }
        } catch (Exception e) {
            log.error("查询钱包绑定关系异常，请求参数: {}", request, e);
            return new WalletBindingQueryResponse();
        }
    }

    @Override
    public Boolean unbindWallet(WalletUnbindRequest request) {
        log.info("解绑钱包，请求参数: {}", request);

        try {
            UnbindWalletRequest unbindWalletRequest = new UnbindWalletRequest();
            unbindWalletRequest.setPassportId(request.getPassportId());
            unbindWalletRequest.setWalletAddress(request.getWalletAddress().toLowerCase());
            Response<Boolean> response = remotePassportService.unbindWallet(unbindWalletRequest);
            return response.getData();
        } catch (Exception e) {
            log.error("解绑钱包异常，请求参数: {}", request, e);
            return false;
        }
    }
}
