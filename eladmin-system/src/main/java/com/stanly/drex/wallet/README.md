# 钱包绑定管理模块

## 模块结构

```
wallet/
├── dto/                           # 数据传输对象
│   ├── WalletBindingInfo.java     # 钱包绑定信息
│   ├── WalletBindingQueryRequest.java   # 查询请求
│   ├── WalletBindingQueryResponse.java  # 查询响应
│   └── WalletUnbindRequest.java   # 解绑请求
├── rest/                          # 控制器层
│   └── WalletBindingController.java     # 钱包绑定控制器
├── service/                       # 服务层
│   ├── WalletBindingService.java  # 服务接口
│   └── impl/
│       └── WalletBindingServiceImpl.java # 服务实现
└── README.md                      # 说明文档
```

## API 接口

### 1. 查询钱包绑定关系

**接口地址：** `POST /api/wallet/binding/query`

**请求参数：**
```json
{
  "userId": "用户ID",
  "walletAddress": "钱包地址",
  "walletType": "钱包类型",
  "page": 1,
  "size": 10
}
```

**响应数据：**
```json
{
  "total": 100,
  "page": 1,
  "size": 10,
  "bindings": [
    {
      "bindingId": "绑定ID",
      "userId": "用户ID",
      "username": "用户名",
      "walletAddress": "钱包地址",
      "walletType": "钱包类型",
      "status": "绑定状态",
      "bindTime": 1640995200000,
      "lastActiveTime": 1640995200000,
      "remark": "备注"
    }
  ]
}
```

### 2. 解绑钱包

**接口地址：** `POST /api/wallet/unbind`

**请求参数：**
```json
{
  "userId": "用户ID",
  "walletAddress": "钱包地址",
  "walletType": "钱包类型",
  "reason": "解绑原因"
}
```

**响应数据：**
```json
true/false
```

## 依赖说明

### Dubbo 服务依赖

本模块依赖 `RemotePassportService` 服务，需要确保该服务接口中包含以下方法：

```java
public interface RemotePassportService {
    
    /**
     * 查询钱包绑定关系
     */
    Response<WalletBindingQueryResponse> queryWalletBinding(WalletBindingQueryRequest request);
    
    /**
     * 解绑钱包
     */
    Response<Boolean> unbindWallet(WalletUnbindRequest request);
}
```

## 使用说明

1. 确保 `RemotePassportService` 服务已正确配置并可用
2. 确保相关的 DTO 类在服务提供方也有对应的定义
3. 根据实际业务需求调整 DTO 字段和验证规则
4. 可以根据需要添加权限控制注解（如 `@PreAuthorize`）

## 注意事项

1. 所有的业务逻辑都通过 Dubbo 调用远程服务实现
2. 本模块只负责接口层的数据转换和异常处理
3. 日志记录包含请求参数和响应结果，便于问题排查
4. 异常处理确保接口的稳定性，避免因远程服务异常导致的系统崩溃
