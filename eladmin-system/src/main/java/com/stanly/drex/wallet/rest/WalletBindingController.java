package com.stanly.drex.wallet.rest;

import com.stanly.admin.annotation.Log;
import com.stanly.drex.wallet.dto.WalletBindingQueryRequest;
import com.stanly.drex.wallet.dto.WalletBindingQueryResponse;
import com.stanly.drex.wallet.dto.WalletUnbindRequest;
import com.stanly.drex.wallet.service.WalletBindingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 钱包绑定管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "钱包绑定管理")
@RequestMapping("/api/wallet")
public class WalletBindingController {

    private final WalletBindingService walletBindingService;

    @PostMapping("/binding/query")
    @Log("查询钱包绑定关系")
    @Operation(summary = "查询钱包绑定关系")
    public ResponseEntity<WalletBindingQueryResponse> queryWalletBinding(@Validated @RequestBody WalletBindingQueryRequest request) {
        log.info("接收到查询钱包绑定关系请求: {}", request);
        try {
            WalletBindingQueryResponse walletBindingQueryResponse = walletBindingService.queryWalletBinding(request);

            return new ResponseEntity<>(walletBindingQueryResponse, HttpStatus.OK);
        } catch (Exception e) {
            log.error("查询钱包绑定关系失败", e);
            return new ResponseEntity<>(new WalletBindingQueryResponse(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/unbind")
    @Log("解绑钱包")
    @Operation(summary = "解绑钱包")
    public ResponseEntity<Boolean> unbindWallet(@Validated @RequestBody WalletUnbindRequest request) {
        log.info("接收到解绑钱包请求: {}", request);
        
        try {
            Boolean b = walletBindingService.unbindWallet(request);
            return new ResponseEntity<>(b, HttpStatus.OK);
        } catch (Exception e) {
            log.error("解绑钱包失败", e);
            return new ResponseEntity<>(false, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
