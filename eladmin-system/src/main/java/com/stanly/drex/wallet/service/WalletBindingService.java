package com.stanly.drex.wallet.service;

import com.stanly.drex.wallet.dto.WalletBindingQueryRequest;
import com.stanly.drex.wallet.dto.WalletBindingQueryResponse;
import com.stanly.drex.wallet.dto.WalletUnbindRequest;

/**
 * 钱包绑定服务接口
 */
public interface WalletBindingService {

    /**
     * 查询钱包绑定关系
     * 
     * @param request 查询请求
     * @return 查询结果
     */
    WalletBindingQueryResponse queryWalletBinding(WalletBindingQueryRequest request);

    /**
     * 解绑钱包
     * 
     * @param request 解绑请求
     * @return 解绑结果
     */
    Boolean unbindWallet(WalletUnbindRequest request);
}
