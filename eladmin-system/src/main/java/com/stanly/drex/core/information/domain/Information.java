/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.domain;

import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.bean.copier.CopyOptions;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2025-05-06
**/
@Entity
@Data
@Table(name="information")
public class Information implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "主键")
    private String id;

    @Column(name = "`type`",nullable = false)
    @NotBlank
    @Schema(name = "类型 podcast、dapp")
    private String type;

    @Column(name = "`dapp_name`")
    @Schema(name = "dapp 名称")
    private String dappName;

    @Column(name = "`dapp_logo`")
    @Schema(name = "dapp logo链接")
    private String dappLogo;

    @Column(name = "`title`",nullable = false)
    @NotBlank
    @Schema(name = "标题")
    private String title;

    @Column(name = "`sub_title`")
    @Schema(name = "副标题")
    private String subTitle;

    @Column(name = "`summary`",nullable = false)
    @NotBlank
    @Schema(name = "摘要")
    private String summary;

    @Column(name = "`content`")
    @Schema(name = "内容")
    private String content;

    @Column(name = "`image`")
    @Schema(name = "图片链接")
    private String image;

    @Column(name = "`link`")
    @Schema(name = "外部链接")
    private String link;

    @Column(name = "`category`")
    @Schema(name = "分类 [GameFi, SocialFi]")
    private String category;

    @Column(name = "`is_recommend`")
    @Schema(name = "推荐置顶")
    private String isRecommend;

    @Column(name = "`status`")
    @Schema(name = "发布状态 0 未发布 1 已发布")
    private Long status;

    @Column(name = "`sort`")
    @Schema(name = "排序值")
    private Integer sort;

    @Column(name = "`created`")
    @Schema(name = "创建时间")
    private Long created;

    @Column(name = "`modified`")
    @Schema(name = "修改时间")
    private Long modified;

    public void copy(Information source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
