/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.rest;

import com.aliyun.oss.model.PutObjectResult;
import com.stanly.admin.annotation.Log;
import com.stanly.admin.utils.FileUtil;
import com.stanly.admin.utils.UploadAliyunOssUtil;
import com.stanly.drex.core.information.domain.Information;
import com.stanly.drex.core.information.service.InformationService;
import com.stanly.drex.core.information.service.dto.InformationQueryCriteria;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.File;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import com.stanly.admin.utils.PageResult;
import com.stanly.drex.core.information.service.dto.InformationDto;
import org.springframework.web.multipart.MultipartFile;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-05-06
**/
@RestController
@RequiredArgsConstructor
@Tag(name = "项目信息配置管理")
@Slf4j
@RequestMapping("/api/information")
public class InformationController {

    private final InformationService informationService;
    private final UploadAliyunOssUtil uploadAliyunOssUtil;

    @Log("导出数据")
    @Operation(summary = "导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('information:list')")
    public void exportInformation(HttpServletResponse response, InformationQueryCriteria criteria) throws IOException {
        informationService.download(informationService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询项目信息配置")
    @Operation(summary = "查询项目信息配置")
    @PreAuthorize("@el.check('information:list')")
    public ResponseEntity<PageResult<InformationDto>> queryInformation(InformationQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(informationService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增项目信息配置")
    @Operation(summary = "新增项目信息配置")
    @PreAuthorize("@el.check('information:add')")
    public ResponseEntity<Object> createInformation(@Validated @RequestBody Information resources){
        if ("extension_home".equals(resources.getPosition())) {
            if (resources.getType() == null) {
                return new ResponseEntity<>("type is required", HttpStatus.BAD_REQUEST);
            }
        }
        
        Information information = informationService.create(resources);

        // 创建成功直接发布
        Boolean publish = informationService.publish(information.getId());
        log.info("directly publish id {} result {} ", resources.getId(), publish);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改项目信息配置")
    @Operation(summary = "修改项目信息配置")
    @PreAuthorize("@el.check('information:edit')")
    public ResponseEntity<Object> updateInformation(@Validated @RequestBody Information resources){
        Information update = informationService.update(resources);

        // 编辑完成先撤回、再重新发布
        Boolean withdraw = informationService.withdraw(update.getId());
        log.info("[update] withdraw id {} result {} ", update.getId(), withdraw);
        Boolean publish = informationService.publish(update.getId());
        log.info("publish id {} result {} ", update.getId(), publish);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping
    @Log("删除项目信息配置")
    @Operation(summary = "删除项目信息配置")
    @PreAuthorize("@el.check('information:del')")
    public ResponseEntity<Object> deleteInformation(@RequestBody String[] ids) {
        for (String id : ids) {
            // 删除成功后直接撤回
            Boolean withdraw = informationService.withdraw(id);
            log.info("[deleteAll] withdraw id {} result {} ", id, withdraw);
        }
        informationService.deleteAll(ids);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @Log("发布项目信息")
    @Operation(summary = "发布项目信息")
    @PostMapping(value = "/publish")
    public ResponseEntity<Object> publishInformation(@RequestBody String id) {
        if (StringUtils.isBlank(id)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if (informationService.publish(id)) {
            return new ResponseEntity<>("发布成功", HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @Log("撤回项目信息")
    @Operation(summary = "撤回项目信息")
    @PostMapping(value = "/withdraw")
    public ResponseEntity<Object> withdrawInformation(@RequestBody String id) {
        if (StringUtils.isBlank(id)) {
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
        if (informationService.withdraw(id)) {
            return new ResponseEntity<>("撤回成功", HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @PostMapping(value = "upload")
    @Log("上传项目图片")
    @Operation(summary = "上传项目图片")
    public ResponseEntity<Object> uploadImage(MultipartFile file, HttpServletRequest request){
        File f = FileUtil.toFile(file);
        String fileName = f.getName();
        PutObjectResult result = uploadAliyunOssUtil.putObject("drex/core/information", fileName, f);
        log.info("uploadImage result: {}", result);
        return ResponseEntity.ok(uploadAliyunOssUtil.getLocation("drex/core/information", fileName));
    }
}