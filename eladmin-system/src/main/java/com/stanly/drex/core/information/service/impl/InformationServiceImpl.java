/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.service.impl;

import com.alibaba.fastjson2.JSON;
import com.drex.core.api.RemoteInformationService;
import com.kikitrade.framework.common.model.Response;
import com.stanly.drex.core.information.domain.Information;
import com.stanly.admin.utils.ValidationUtil;
import com.stanly.admin.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import com.stanly.drex.core.information.repository.InformationRepository;
import com.stanly.drex.core.information.service.InformationService;
import com.stanly.drex.core.information.service.dto.InformationDto;
import com.stanly.drex.core.information.service.dto.InformationQueryCriteria;
import com.stanly.drex.core.information.service.mapstruct.InformationMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import com.stanly.admin.utils.PageUtil;
import com.stanly.admin.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import jakarta.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

import com.stanly.admin.utils.PageResult;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2025-05-06
**/
@Service
@Slf4j
@RequiredArgsConstructor
public class InformationServiceImpl implements InformationService {

    private final InformationRepository informationRepository;
    private final InformationMapper informationMapper;

    @DubboReference
    private RemoteInformationService remoteInformationService;

    @Override
    public PageResult<InformationDto> queryAll(InformationQueryCriteria criteria, Pageable pageable){
        // 如果指定了position，使用position的自定义排序
        if (criteria != null && criteria.getPosition() != null && !criteria.getPosition().trim().isEmpty()) {
            // 检查是否只有position条件
            if (isOnlyPositionCriteria(criteria)) {
                Page<Information> page = informationRepository.findByPositionWithCustomSort(criteria.getPosition(), pageable);
                return PageUtil.toPage(page.map(informationMapper::toDto));
            }
        }

        // 如果没有查询条件，使用全局自定义排序
        if (isEmptyCriteria(criteria)) {
            return queryAllWithCustomSort(pageable);
        }

        // 有其他查询条件时，使用原有的查询方式
        Page<Information> page = informationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(informationMapper::toDto));
    }

    /**
     * 检查是否只有position条件
     */
    private boolean isOnlyPositionCriteria(InformationQueryCriteria criteria) {
        if (criteria == null || criteria.getPosition() == null) return false;
        return criteria.getId() == null &&
               criteria.getType() == null &&
               criteria.getDappName() == null &&
               criteria.getTitle() == null &&
               criteria.getCategory() == null &&
               criteria.getIsRecommend() == null;
    }

    /**
     * 检查查询条件是否为空
     */
    private boolean isEmptyCriteria(InformationQueryCriteria criteria) {
        if (criteria == null) return true;
        return criteria.getId() == null &&
               criteria.getType() == null &&
               criteria.getDappName() == null &&
               criteria.getTitle() == null &&
               criteria.getCategory() == null &&
               criteria.getIsRecommend() == null &&
               criteria.getPosition() == null;
    }

    @Override
    public List<InformationDto> queryAll(InformationQueryCriteria criteria){
        // 如果指定了position，使用position的自定义排序
        if (criteria != null && criteria.getPosition() != null && !criteria.getPosition().trim().isEmpty()) {
            // 检查是否只有position条件
            if (isOnlyPositionCriteria(criteria)) {
                List<Information> informationList = informationRepository.findByPositionWithCustomSort(criteria.getPosition());
                return informationMapper.toDto(informationList);
            }
        }

        // 如果没有查询条件，使用全局自定义排序
        if (isEmptyCriteria(criteria)) {
            return queryAllWithCustomSort();
        }

        // 有其他查询条件时，使用原有的查询方式
        return informationMapper.toDto(informationRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    public PageResult<InformationDto> queryAllWithCustomSort(Pageable pageable) {
        Page<Information> page = informationRepository.findAllWithCustomSort(pageable);
        return PageUtil.toPage(page.map(informationMapper::toDto));
    }

    @Override
    public List<InformationDto> queryAllWithCustomSort() {
        List<Information> informationList = informationRepository.findAllWithCustomSort();
        return informationMapper.toDto(informationList);
    }

    @Override
    @Transactional
    public InformationDto findById(String id) {
        Information information = informationRepository.findById(id).orElseGet(Information::new);
        ValidationUtil.isNull(information.getId(),"Information","id",id);
        return informationMapper.toDto(information);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Information create(Information resources) {
        // 使用UUID生成ID
        resources.setId(cn.hutool.core.util.IdUtil.randomUUID());
        resources.setStatus(0L); // 新建时默认未发布状态
        // 设置创建时间
        if (resources.getCreated() == null) {
            resources.setCreated(System.currentTimeMillis());
        }

        // 处理discovery_blog位置的置顶逻辑
        handleDiscoveryBlogRecommend(resources, true);

        return informationRepository.save(resources);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Information update(Information resources) {
        Information information = informationRepository.findById(resources.getId()).orElseGet(Information::new);
        ValidationUtil.isNull( information.getId(),"Information","id",resources.getId());
        information.copy(resources);
        // 设置修改时间
        if (information.getModified() == null) {
            information.setModified(System.currentTimeMillis());
        }
        return informationRepository.save(information);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            informationRepository.deleteById(id);
        }
    }

    @Override
    public void download(List<InformationDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (InformationDto information : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("类型 podcast、dapp", information.getType());
            map.put("dapp 名称", information.getDappName());
            map.put("dapp logo链接", information.getDappLogo());
            map.put("标题", information.getTitle());
            map.put("副标题", information.getSubTitle());
            map.put("摘要", information.getSummary());
            map.put("内容", information.getContent());
            map.put("图片链接", information.getImage());
            map.put("外部链接", information.getLink());
            map.put("分类 [GameFi, SocialFi]", information.getCategory());
            map.put("推荐置顶", information.getIsRecommend());
            map.put("排序值", information.getSort());
            map.put("创建时间", information.getCreated());
            map.put("修改时间", information.getModified());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Boolean publish(String id) {
        InformationDto informationDto = findById(id);
        if (informationDto.getStatus() != 0) {
            log.error("publish illegal, current status not 0");
            return false;
        }
        com.drex.core.api.response.InformationDTO information = new com.drex.core.api.response.InformationDTO();
        BeanUtils.copyProperties(informationDto, information);
        information.setIsRecommend(Boolean.parseBoolean(informationDto.getIsRecommend()));
        if (StringUtils.isNotBlank(informationDto.getCategory())) {
            List<String> categories = JSON.parseArray(informationDto.getCategory(), String.class);
            information.setCategory(categories);
        }

        log.info("begin publish information {}", information);

        Response<Boolean> booleanResponse = remoteInformationService.saveInformation(information);
        log.info("end publish booleanResponse {}", booleanResponse);
        if (booleanResponse.getData()) {
            informationDto.setStatus(1L); // 发布成功后更新状态为已发布 1

            Information resources = new Information();
            BeanUtils.copyProperties(informationDto, resources);
            update(resources);
        }
        return booleanResponse.getData();
    }

    @Override
    public Boolean withdraw(String id) {
        InformationDto informationDto = findById(id);
        if (informationDto == null) {
            return Boolean.FALSE;
        }
        if (informationDto.getStatus() != 1) {
            log.error("withdraw illegal, current status not 1");
            return false;
        }
        log.info("begin withdraw information {}", informationDto);

        Response<Boolean> booleanResponse = remoteInformationService.deleteInformation(informationDto.getId());
        log.info("end withdraw booleanResponse {}", booleanResponse);
        if (booleanResponse.getData()) {
            informationDto.setStatus(0L); // 撤回成功后更新状态为未发布 0

            Information resources = new Information();
            BeanUtils.copyProperties(informationDto, resources);
            update(resources);
        }
        return booleanResponse.getData();
    }
}