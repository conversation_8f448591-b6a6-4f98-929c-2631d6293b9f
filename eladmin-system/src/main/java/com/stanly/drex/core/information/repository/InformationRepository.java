/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.drex.core.information.repository;

import com.stanly.drex.core.information.domain.Information;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* @website https://eladmin.vip
* <AUTHOR>
* @date 2025-05-06
**/
public interface InformationRepository extends JpaRepository<Information, String>, JpaSpecificationExecutor<Information> {

    /**
     * 自定义排序查询：按照 position、isRecommend（true排前面）、sort（值越大越靠前）排序
     */
    @Query("SELECT i FROM Information i ORDER BY i.position ASC, " +
           "CASE WHEN i.isRecommend = 'true' THEN 0 ELSE 1 END ASC, " +
           "i.sort DESC")
    List<Information> findAllWithCustomSort();

    /**
     * 自定义排序查询（分页）：按照 position、isRecommend（true排前面）、sort（值越大越靠前）排序
     */
    @Query("SELECT i FROM Information i ORDER BY i.position ASC, " +
           "CASE WHEN i.isRecommend = 'true' THEN 0 ELSE 1 END ASC, " +
           "i.sort DESC")
    Page<Information> findAllWithCustomSort(Pageable pageable);

    /**
     * 根据position查询并按照isRecommend和sort排序
     */
    @Query("SELECT i FROM Information i WHERE i.position = :position " +
           "ORDER BY CASE WHEN i.isRecommend = 'true' THEN 0 ELSE 1 END ASC, " +
           "i.sort DESC")
    Page<Information> findByPositionWithCustomSort(String position, Pageable pageable);

    /**
     * 根据position查询并按照isRecommend和sort排序（不分页）
     */
    @Query("SELECT i FROM Information i WHERE i.position = :position " +
           "ORDER BY CASE WHEN i.isRecommend = 'true' THEN 0 ELSE 1 END ASC, " +
           "i.sort DESC")
    List<Information> findByPositionWithCustomSort(String position);

}