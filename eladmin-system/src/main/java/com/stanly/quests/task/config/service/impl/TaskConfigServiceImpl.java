/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.drex.activity.task.model.dto.Award;
import com.drex.activity.task.model.dto.TaskConfigDTO;
import com.stanly.admin.utils.*;
import com.stanly.quests.task.config.domain.Reward;
import com.stanly.quests.task.config.domain.TaskConfig;
import com.stanly.quests.task.config.domain.TaskConfigManagerDTO;
import com.stanly.quests.task.config.domain.TimingPublishDTO;
import com.stanly.quests.task.config.repository.TaskConfigRepository;
import com.stanly.quests.task.config.service.TaskConfigService;
import com.stanly.quests.task.config.service.dto.TaskConfigDto;
import com.stanly.quests.task.config.service.dto.TaskConfigQueryCriteria;
import com.stanly.quests.task.config.service.mapstruct.TaskConfigMapper;
import com.stanly.quests.task.config.service.proxy.TaskRemoteService;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;

/**
* @website https://eladmin.vip
* @description 服务实现
* <AUTHOR>
* @date 2024-08-02
**/
@Service
@RequiredArgsConstructor
@Slf4j
public class TaskConfigServiceImpl implements TaskConfigService {
    private final TaskConfigRepository taskConfigRepository;
    private final TaskConfigMapper taskConfigMapper;
    private final IdUtils idUtils;
    private final TaskRemoteService taskRemoteService;
    private final RedisUtils redisUtils;

    @Override
    public PageResult<TaskConfigDto> queryAll(TaskConfigQueryCriteria criteria, Pageable pageable){
        if(criteria.getPublishTime() != null){
            Date date = TimeUtil.addHour(new Date(criteria.getPublishTime()), 8);
            criteria.setPublishTime(TimeUtil.format(date));
        }
        Page<TaskConfig> page = taskConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder),pageable);
        return PageUtil.toPage(page.map(taskConfigMapper::toDto));
    }

    @Override
    public List<TaskConfigDto> queryAll(TaskConfigQueryCriteria criteria){
        return taskConfigMapper.toDto(taskConfigRepository.findAll((root, criteriaQuery, criteriaBuilder) -> QueryHelp.getPredicate(root,criteria,criteriaBuilder)));
    }

    @Override
    @Transactional
    public TaskConfigDto findById(String id) {
        TaskConfig taskConfig = taskConfigRepository.findById(id).orElseGet(TaskConfig::new);
        ValidationUtil.isNull(taskConfig.getId(),"TaskConfig","id",id);
        return taskConfigMapper.toDto(taskConfig);
    }

    @Override
    public TaskConfigDTO findByTaskId(String taskId) {
        return toDTO(taskConfigRepository.findByTaskId(taskId));
    }

    @Override
    public List<TaskConfigDTO> findByTaskIds(List<String> taskIds) {
        List<TaskConfig> taskConfigs = taskConfigRepository.findByTaskIdIn(taskIds);
        if(CollectionUtils.isNotEmpty(taskConfigs)){
            List<TaskConfigDTO> taskConfigDTOS = new ArrayList<>();
            taskConfigs.forEach(taskConfig -> {
                taskConfigDTOS.add(toDTO(taskConfig));
            });
            return taskConfigDTOS;
        }
        return new ArrayList<>();
    }

    @Override
    public TaskConfigDTO importJSON(TaskConfigDTO taskConfigDTO) {
        log.info("importJSON:{}", taskConfigDTO);
        TaskConfig taskConfig = toDO(taskConfigDTO);
        TaskConfig config = null;
        if(taskConfig.getTaskId() != null){
             config = taskConfigRepository.findByTaskId(taskConfig.getTaskId());
        }
        if(config != null){
            taskConfig.setId(config.getId());
        }else{
            taskConfig.setId(IdUtil.simpleUUID());
            taskConfig.setTaskId(taskConfigDTO.getTaskId() != null ? taskConfigDTO.getTaskId() : idUtils.generateTaskId());
        }
        taskConfigRepository.save(taskConfig);
        return taskConfigDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(TaskConfigManagerDTO resources) {
        resources.setId(IdUtil.simpleUUID());
        // 生成递增的 taskId，不依赖于 saasId
        resources.setTaskId(idUtils.generateTaskId());

        if(resources.getEndTime() == null){
            resources.setEndTime(new Timestamp(4859769600000L));
        }

        TaskConfig taskConfig = new TaskConfig();
        BeanUtils.copyProperties(resources,taskConfig);
        Date startDate = TimeUtil.addHour(new Date(resources.getStartTime().getTime()), 8);
        taskConfig.setStartTime(new Timestamp(startDate.getTime()));
        Date endDate = TimeUtil.addHour(new Date(resources.getEndTime().getTime()), 8);
        taskConfig.setEndTime(new Timestamp(endDate.getTime()));
        if(resources.getRewards() != null){
            resources.getRewards().forEach(reward -> {
                reward.setRewardCurrency(reward.getRewardCurrency() == null ? resources.getRewardType() : reward.getRewardCurrency());
            });
            taskConfig.setRewards(JSON.toJSONString(resources.getRewards()));
        }
        resources.setRewardCurrency(resources.getRewardCurrency() == null ? resources.getRewardType() : resources.getRewardCurrency());
        taskConfigRepository.save(taskConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TaskConfigManagerDTO resources) {
        TaskConfig taskConfig = taskConfigRepository.findById(resources.getId()).orElseGet(TaskConfig::new);
        ValidationUtil.isNull( taskConfig.getId(),"TaskConfig","id",resources.getId());

        TaskConfig taskConfigDTO = new TaskConfig();
        BeanUtils.copyProperties(resources, taskConfigDTO);

        if(resources.getRewards() != null){
            resources.getRewards().forEach(reward -> {
                reward.setRewardCurrency(reward.getRewardCurrency() == null ? resources.getRewardType() : reward.getRewardCurrency());
            });
            taskConfigDTO.setRewards(JSON.toJSONString(resources.getRewards()));
        }
        Date startDate = TimeUtil.addHour(new Date(resources.getStartTime().getTime()), 8);
        taskConfigDTO.setStartTime(new Timestamp(startDate.getTime()));
        Date endDate = TimeUtil.addHour(new Date(resources.getEndTime().getTime()), 8);
        taskConfigDTO.setEndTime(new Timestamp(endDate.getTime()));
        taskConfigDTO.setPublishTime(taskConfig.getPublishTime());
        taskConfigRepository.save(taskConfigDTO);
    }

    @Override
    public void deleteAll(String[] ids) {
        for (String id : ids) {
            taskConfigRepository.deleteById(id);
            taskRemoteService.deleteTaskConfig(id);
        }
    }

    @Override
    public void download(List<TaskConfigDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (TaskConfigDto taskConfig : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("SaasId", taskConfig.getSaasId());
            map.put("任务 id", taskConfig.getTaskId());
            map.put("分组 id", taskConfig.getGroupId());
            map.put("是否在分组内 (0 否 1 是)", taskConfig.getIsGroup());
            map.put("是否列表展示 (0 否 1 是)", taskConfig.getShowList());
            map.put("积分流水描述", taskConfig.getLedgerTitle());
            map.put("任务标题", taskConfig.getTitle());
            map.put("任务标题 (APP)", taskConfig.getTitleApp());
            map.put("任务标题 (PC)", taskConfig.getTitlePc());
            map.put("任务描述 (APP 非会员)", taskConfig.getTitleDescAppNormal());
            map.put("任务描述 (APP 会员 L1)", taskConfig.getTitleDescAppL1());
            map.put("任务描述 (PC 非会员)", taskConfig.getTitleDescPcNormal());
            map.put("任务描述 (PC 会员 L1)", taskConfig.getTitleDescPcL1());
            map.put("任务角标内容", taskConfig.getLabelName());
            map.put("任务角标颜色", taskConfig.getLabelColor());
            map.put("任务状态 (ACTIVE、DISABLE)", taskConfig.getStatus());
            map.put("任务开始时间，utc 毫秒时间戳", taskConfig.getStartTime());
            map.put("任务结束时间，utc 毫秒时间戳", taskConfig.getEndTime());
            map.put("任务事件编号", taskConfig.getCode());
            map.put("前端需要显示的事件，目前主要映射 icon", taskConfig.getShowCode());
            map.put("任务次数上限 (非会员)", taskConfig.getLimitCountNormal());
            map.put("任务次数上限 (会员 L1)", taskConfig.getLimitCountL1());
            map.put("任务列表图片", taskConfig.getTaskListImage());
            map.put("任务详情图片", taskConfig.getTaskDetailImage());
            map.put("发奖频率，每完成 n 次任务，发一次奖", taskConfig.getRewardFrequency());
            map.put("任务刷新周期", taskConfig.getCycle());
            map.put("进度计算方式", taskConfig.getProgressType());
            map.put("奖品计算方式", taskConfig.getRewardForm());
            map.put("积分领取方式", taskConfig.getProvideType());
            map.put("奖励类型", taskConfig.getRewardType());
            map.put("奖励数量", taskConfig.getRewardAmount());
            map.put("奖励币种", taskConfig.getRewardCurrency());
            map.put("展示奖励数量", taskConfig.getShowRewardAmount());
            map.put("奖励专属的会员等级", taskConfig.getRewardVipLevel());
            map.put("随机奖励的标号", taskConfig.getRewardIndex());
            map.put("任务排序", taskConfig.getOrder());
            map.put("任务所属模块 twitter、discord", taskConfig.getDomain());
            map.put("twitter 被关注的人", taskConfig.getTwitterFollow());
            map.put("发帖包含的关键字", taskConfig.getTwitterKeyword());
            map.put("贴文追加的文案", taskConfig.getTwitterRandomAppendText());
            map.put("贴文替换的文案", taskConfig.getTwitterRandomText());
            map.put("回复任务要求指定的用户", taskConfig.getTwitterKols());
            map.put("twitter 用户名包含的关键字", taskConfig.getTwitterUsername());
            map.put("查最近帖文", taskConfig.getLastPost());
            map.put("discord 服务器 id", taskConfig.getDiscordGuild());
            map.put("拥有 discord 某个角色", taskConfig.getDiscordGuildRole());
            map.put("任务要求的 vip 等级，+0: 普通用户以上，1: 会员专享", taskConfig.getVipLevel());
            map.put("前端需要显示的按钮，默认 0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击 go 后才会出现 verify 按钮", taskConfig.getBtn());
            map.put("是否显示任务 showProgress 进度条", taskConfig.getShowProgress());
            map.put("任务的详情页", taskConfig.getUrl());
            map.put("任务描述下的按钮文字", taskConfig.getLinkName());
            map.put("任务描述下的按钮链接", taskConfig.getLinkUrl());
            map.put("任务所属渠道，pc | app", taskConfig.getChannel());
            map.put("任务需要显示的页面位置", taskConfig.getPosition());
            map.put("是否回调注册任务，默认 true，库里 null 表示 true", taskConfig.getCallRegister());
            map.put("任务状态判断，默认任务表状态 twitterAuth：twitter 授权过 discordAuth: discord 授权过", taskConfig.getTaskStatusCondition());
            map.put("是否跳过验证", taskConfig.getSkipVerification());
            map.put("任务所属客户端类型，android｜ios", taskConfig.getClientType());
            map.put("是否需要回调osp", taskConfig.getOspCallBack());
            map.put("osp事件类型", taskConfig.getTaskType());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 发布任务
     *
     * @param taskConfig /
     * @param isGray     是否灰度发布
     */
    @Override
    public boolean publish(TaskConfigManagerDTO taskConfig, boolean isGray) {
        TaskConfigDTO taskConfigDTO = fromVO(taskConfig);
        if(isGray){
             buildGrayTaskConfigDTO(taskConfigDTO);
        }
        return taskRemoteService.syncTaskConfig(taskConfigDTO, isGray);
    }

    @Override
    public void timingPublish(TimingPublishDTO timingPublishDTO) {
        Date date = TimeUtil.addHour(new Date(timingPublishDTO.getTime().getTime()), 8);
        redisUtils.hset("timing_publish", timingPublishDTO.getTaskId(), date.getTime());
        TaskConfig taskConfig = taskConfigRepository.findByTaskId(timingPublishDTO.getTaskId());
        taskConfig.setPublishTime(new Timestamp(date.getTime()));
        taskConfigRepository.save(taskConfig);
    }

    private void buildGrayTaskConfigDTO(TaskConfigDTO taskConfig){
        taskConfig.setShowList(true);
        taskConfig.setStartTime(1733011200000L);
    }

    private TaskConfigDTO toDTO(TaskConfig taskConfig){

        TaskConfigDTO taskConfigDTO = new TaskConfigDTO();
        BeanUtils.copyProperties(taskConfig, taskConfigDTO);

        taskConfigDTO.setStartTime(taskConfig.getStartTime().getTime());
        taskConfigDTO.setEndTime(taskConfig.getEndTime().getTime());
        taskConfigDTO.setShowProgress(Boolean.parseBoolean(taskConfig.getShowProgress()));
        taskConfigDTO.setShowList(Boolean.parseBoolean(taskConfig.getShowList()));


        Map<String, List<Award>> rewardMap = new HashMap<>();
        Award award = new Award();
        award.setAmount(taskConfig.getRewardAmount());
        award.setShowAmount(taskConfig.getRewardShowAmount());
        award.setCurrency(taskConfig.getRewardCurrency());
        award.setVipLevel("NORMAL");
        award.setType(taskConfig.getRewardType());
        List<Award> rewardList = new ArrayList<>();
        rewardList.add(award);
        if(StringUtils.isNotBlank(taskConfig.getRewards()) && JSONUtil.isTypeJSONArray(taskConfig.getRewards())) {
            JSON.parseArray(taskConfig.getRewards(), Reward.class).forEach(item -> {
                Award awardExt = new Award();
                awardExt.setAmount(item.getRewardAmount());
                awardExt.setShowAmount(item.getShowRewardAmount());
                awardExt.setCurrency(item.getRewardCurrency());
                awardExt.setVipLevel("NORMAL");
                awardExt.setType(item.getRewardType());
                rewardList.add(awardExt);
            });
        }
        rewardMap.put("0", rewardList);
        taskConfigDTO.setReward(rewardMap);

        //status
        if( StringUtils.isNotEmpty(taskConfig.getStatus())){
            taskConfigDTO.setStatus(taskConfig.getStatus());
        }else {
            taskConfigDTO.setStatus("ACTIVE");
        }

        //cycle
        taskConfigDTO.setCycle(taskConfig.getCycle());

       taskConfigDTO.setConnectUrl(taskConfig.getConnectUrl());

        return taskConfigDTO;
    }


    private TaskConfig toDO(TaskConfigDTO taskConfigDTO){

        TaskConfig taskConfig = new TaskConfig();
        BeanUtils.copyProperties(taskConfigDTO, taskConfig);

        taskConfig.setStartTime(new Timestamp(taskConfigDTO.getStartTime()));
        taskConfig.setEndTime(new Timestamp(taskConfigDTO.getEndTime()));
        taskConfig.setShowProgress(String.valueOf(taskConfigDTO.getShowProgress()));
        taskConfig.setShowList(String.valueOf(taskConfigDTO.getShowList()));



        //reward
        if(taskConfigDTO.getReward() != null){
            if(taskConfigDTO.getReward().get("0").size() >= 1){
                taskConfig.setRewardAmount(taskConfigDTO.getReward().get("0").get(0).getAmount());
                taskConfig.setRewardShowAmount(taskConfigDTO.getReward().get("0").get(0).getShowAmount());
                taskConfig.setRewardType(taskConfigDTO.getReward().get("0").get(0).getType());
                taskConfig.setRewardCurrency(taskConfigDTO.getReward().get("0").get(0).getCurrency());
            }
            if(taskConfigDTO.getReward().get("0").size() >= 2){
                List<Reward> list = new ArrayList<>();
                for(int i = 1; i < taskConfigDTO.getReward().get("0").size(); i++){
                    Reward award = new Reward();
                    award.setRewardAmount(taskConfigDTO.getReward().get("0").get(i).getAmount());
                    award.setShowRewardAmount(taskConfigDTO.getReward().get("0").get(i).getShowAmount());
                    award.setRewardType(taskConfigDTO.getReward().get("0").get(i).getType());
                    award.setRewardCurrency(taskConfigDTO.getReward().get("0").get(i).getCurrency());
                    list.add(award);
                }
                taskConfig.setRewards(JSON.toJSONString(list));
            }
        }

        //status
        taskConfig.setStatus("DISABLE");

        //cycle
        if(taskConfigDTO.getCycle() != null){
            taskConfig.setCycle(taskConfigDTO.getCycle());
        }

        return taskConfig;
    }

    public TaskConfigDTO fromVO(TaskConfigManagerDTO taskConfig){
        TaskConfigDTO taskConfigDTO = new TaskConfigDTO();
        BeanUtils.copyProperties(taskConfig, taskConfigDTO);

        taskConfigDTO.setStartTime(TimeUtil.parseWithUtc(TimeUtil.format(taskConfig.getStartTime()), "yyyy-MM-dd HH:mm:ss").getTime());
        taskConfigDTO.setEndTime(TimeUtil.parseWithUtc(TimeUtil.format(taskConfig.getEndTime()), "yyyy-MM-dd HH:mm:ss").getTime());
        taskConfigDTO.setShowProgress(Boolean.parseBoolean(taskConfig.getShowProgress()));
        taskConfigDTO.setShowList(Boolean.parseBoolean(taskConfig.getShowList()));

        //title
        taskConfigDTO.setTitle(taskConfig.getTitle());
        taskConfigDTO.setDesc(taskConfig.getDesc());


        //reward
        Map<String, List<Award>> rewardMap = new HashMap<>();
        List<Award> rewardList = new ArrayList<>();
        Award award = new Award();
        award.setAmount(taskConfig.getRewardAmount());
        award.setShowAmount(taskConfig.getShowRewardAmount());
        award.setCurrency(taskConfig.getRewardCurrency());
        award.setVipLevel("NORMAL");
        award.setType(taskConfig.getRewardType());
        rewardList.add(award);
        if(CollectionUtils.isNotEmpty(taskConfig.getRewards())){
            taskConfig.getRewards().forEach(item -> {
                Award awardExt = new Award();
                awardExt.setAmount(item.getRewardAmount());
                awardExt.setShowAmount(item.getShowRewardAmount());
                awardExt.setCurrency(item.getRewardCurrency());
                awardExt.setVipLevel("NORMAL");
                awardExt.setIndex(item.getRewardIndex());
                awardExt.setType(item.getRewardType());
                rewardList.add(awardExt);
            });
        }
        rewardMap.put("0", rewardList);
        taskConfigDTO.setReward(rewardMap);

        //status
        if( StringUtils.isNotEmpty(taskConfig.getStatus())){
            taskConfigDTO.setStatus(taskConfig.getStatus());
        }else {
            taskConfigDTO.setStatus("ACTIVE");
        }

        //cycle
        if(StringUtils.isNotEmpty(taskConfig.getCycle())){
            taskConfigDTO.setCycle(taskConfig.getCycle());
        }

        taskConfigDTO.setAttr(taskConfig.getAttr());
        return taskConfigDTO;
    }
}
