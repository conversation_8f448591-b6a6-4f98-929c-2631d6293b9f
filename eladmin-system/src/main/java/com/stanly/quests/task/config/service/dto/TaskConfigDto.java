/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service.dto;

import com.stanly.quests.task.config.domain.Reward;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-08-02
**/
@Data
public class TaskConfigDto implements Serializable {

    /** 数据主键 */
    private String id;


    /** 任务id */
    private String taskId;

    /** 是否列表展示(0 否 1 是) */
    private String showList;

    /** 积分流水描述 */
    private String ledgerTitle;

    /** 任务标题 */
    private String title;




    /** 任务状态(ACTIVE、DISABLE) */
    private String status;

    /** 任务开始时间，utc毫秒时间戳 */
    private Timestamp startTime;

    /** 任务结束时间，utc毫秒时间戳 */
    private Timestamp endTime;

    /** 任务事件编号 */
    private String code;

    /** 前端需要显示的事件，目前主要映射icon */
    private String showCode;

    /** 任务次数上限(非会员) */
    private Integer limitCountNormal;

    /** 任务次数上限(会员L1) */
    private Integer limitCountL1;


    /** 发奖频率，每完成n次任务，发一次奖 */
    private Integer rewardFrequency;

    /** 任务刷新周期 */
    private String cycle;

    /** 进度计算方式 */
    private String progressType;

    /** 奖品计算方式 */
    private String rewardForm;

    /** 积分领取方式 */
    private String provideType;

    /** 奖励类型 */
    private String rewardType;

    /** 奖励数量 */
    private String rewardAmount;

    /** 展示奖励数量 */
    private String showRewardAmount;

    /** 奖励币种 */
    private String rewardCurrency;

    /** 奖励专属的会员等级 */
    private String rewardVipLevel;

    /** 随机奖励的标号 */
    private Integer rewardIndex;

    /** 任务排序 */
    private Integer order;

    /** 任务所属模块twitter、discord */
    private String domain;

    /** 任务要求的vip等级，+0: 普通用户以上，1: 会员专享 */
    private String vipLevel;

    /** 前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮 */
    private Integer btn;

    /** 是否显示任务showProgress进度条 */
    private String showProgress;

    /** 任务的详情页 */
    private String url;

    private String attr;


    private List<Reward> rewards;

    /** 计划发布时间 */
    private Timestamp publishTime;

    /** appId */
    private String appId;

    private String connectUrl;
}
