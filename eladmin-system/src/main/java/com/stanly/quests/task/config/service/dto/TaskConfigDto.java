/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package com.stanly.quests.task.config.service.dto;

import com.stanly.quests.task.config.domain.Reward;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @website https://eladmin.vip
* @description /
* <AUTHOR>
* @date 2024-08-02
**/
@Data
public class TaskConfigDto implements Serializable {

    /** 数据主键 */
    private String id;

    /** SaasId */
    private String saasId;

    /** 任务id */
    private String taskId;

    /** 分组id */
    private String groupId;

    /** 是否在分组内(0 否 1 是) */
    private String isGroup;

    /** 是否列表展示(0 否 1 是) */
    private String showList;

    /** 积分流水描述 */
    private String ledgerTitle;

    /** 任务标题 */
    private String title;

    /** 任务标题(APP) */
    private String titleApp;

    /** 任务标题(PC) */
    private String titlePc;

    /** 任务描述(APP非会员) */
    private String titleDescAppNormal;

    /** 任务描述(APP会员L1) */
    private String titleDescAppL1;

    /** 任务描述(PC非会员) */
    private String titleDescPcNormal;

    /** 任务描述(PC会员L1) */
    private String titleDescPcL1;

    /** 任务角标内容 */
    private String labelName;

    /** 任务角标颜色 */
    private String labelColor;

    /** 任务状态(ACTIVE、DISABLE) */
    private String status;

    /** 任务开始时间，utc毫秒时间戳 */
    private Timestamp startTime;

    /** 任务结束时间，utc毫秒时间戳 */
    private Timestamp endTime;

    /** 任务事件编号 */
    private String code;

    /** 前端需要显示的事件，目前主要映射icon */
    private String showCode;

    /** 任务次数上限(非会员) */
    private Integer limitCountNormal;

    /** 任务次数上限(会员L1) */
    private Integer limitCountL1;

    /** 任务列表图片 */
    private String taskListImage;

    /** 任务详情图片 */
    private String taskDetailImage;

    /** 发奖频率，每完成n次任务，发一次奖 */
    private Integer rewardFrequency;

    /** 任务刷新周期 */
    private String cycle;

    /** 进度计算方式 */
    private String progressType;

    /** 奖品计算方式 */
    private String rewardForm;

    /** 积分领取方式 */
    private String provideType;

    /** 奖励类型 */
    private String rewardType;

    /** 奖励数量 */
    private String rewardAmount;

    /** 展示奖励数量 */
    private String showRewardAmount;

    /** 奖励币种 */
    private String rewardCurrency;

    /** 奖励专属的会员等级 */
    private String rewardVipLevel;

    /** 随机奖励的标号 */
    private Integer rewardIndex;

    /** 任务排序 */
    private Integer order;

    /** 任务所属模块twitter、discord */
    private String domain;

    /** twitter被关注的人 */
    private String twitterFollow;

    /** 发帖包含的关键字 */
    private String twitterKeyword;

    /** 贴文追加的文案 */
    private String twitterRandomAppendText;

    /** 贴文替换的文案 */
    private String twitterRandomText;

    /** 回复任务要求指定的用户 */
    private String twitterKols;

    /** twitter用户名包含的关键字 */
    private String twitterUsername;

    /** 查最近帖文 */
    private String lastPost;

    /** discord服务器id */
    private String discordGuild;

    /** 拥有discord某个角色 */
    private String discordGuildRole;

    /** 任务要求的vip等级，+0: 普通用户以上，1: 会员专享 */
    private String vipLevel;

    /** 前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮 */
    private Integer btn;

    /** 是否显示任务showProgress进度条 */
    private String showProgress;

    /** 任务的详情页 */
    private String url;

    /** 任务描述下的按钮文字 */
    private String linkName;

    /** 任务描述下的按钮链接 */
    private String linkUrl;

    /** 任务所属渠道，pc | app */
    private String channel;

    /** 任务需要显示的页面位置 */
    private String position;

    /** 是否回调注册任务，默认true，库里null表示true */
    private String callRegister;

    /** 任务状态判断，默认任务表状态twitterAuth：twitter授权过discordAuth: discord授权过 */
    private String taskStatusCondition;

    /** 是否跳过验证 */
    private String skipVerification;

    /** 任务所属客户端类型，android｜ios */
    private String clientType;

    /** 是否需要回调osp */
    private Integer ospCallBack;

    /** osp事件类型 */
    private String taskType;

    private List<Reward> rewards;

    /** 计划发布时间 */
    private Timestamp publishTime;

    /** appId */
    private String appId;

    /** chainId */
    private String chainId;

    /** discord授权地址 */
    private String discordAuthUrlApp;

    /** discord pcs授权地址 */
    private String discordAuthUrlPc;

    /** 校验任务是否执行 */
    private String checkReward;
}
