/*
 *  Copyright 2019-2020 <PERSON>
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.stanly.quests.task.config.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @website https://eladmin.vip
 * @description /
 * <AUTHOR>
 * @date 2024-11-18
 **/
@Entity
@Data
@Table(name="task_config")
public class TaskConfig implements Serializable {

    @Id
    @Column(name = "`id`")
    @Schema(name = "数据主键")
    private String id;

    @Column(name = "`app_id`",nullable = false)
    @NotBlank
    @Schema(name = "app_id")
    private String appId;

    @Column(name = "`task_id`")
    @Schema(name = "任务id")
    private String taskId;

    @Column(name = "`show_list`")
    @Schema(name = "是否列表展示(0 否 1 是)")
    private String showList;

    @Column(name = "`ledger_title`")
    @Schema(name = "积分流水描述")
    private String ledgerTitle;

    @Column(name = "`title`")
    @Schema(name = "任务标题")
    private String title;

    @Column(name = "desc")
    @Schema(name = "任务描述")
    private String desc;

    @Column(name = "`status`")
    @Schema(name = "任务状态(ACTIVE、DISABLE)")
    private String status;

    @Column(name = "`start_time`")
    @Schema(name = "任务开始时间，utc毫秒时间戳")
    private Timestamp startTime;

    @Column(name = "`end_time`")
    @Schema(name = "任务结束时间，utc毫秒时间戳")
    private Timestamp endTime;

    @Column(name = "`code`")
    @Schema(name = "任务事件编号")
    private String code;

    @Column(name = "`reward_frequency`")
    @Schema(name = "发奖频率，每完成n次任务，发一次奖")
    private Integer rewardFrequency;

    @Column(name = "`cycle`")
    @Schema(name = "任务刷新周期")
    private String cycle;

    @Column(name = "`order`")
    @Schema(name = "任务排序")
    private Integer order;

    @Column(name = "`domain`")
    @Schema(name = "任务所属模块twitter、discord")
    private String domain;

    @Column(name = "`btn`")
    @Schema(name = "前端需要显示的按钮，默认0, 0:不显示 1:go 2:go and verify，3: connect，4: 点击go后才会出现verify按钮")
    private Integer btn;

    @Column(name = "`show_progress`")
    @Schema(name = "是否显示任务showProgress进度条")
    private String showProgress;

    @Column(name = "`url`")
    @Schema(name = "任务的详情页")
    private String url;

    @Column(name = "reward_amount")
    @Schema(name = "奖励数量")
    private String rewardAmount;

    @Column(name = "reward_type")
    @Schema(name = "奖励类型")
    private String rewardType;

    @Column(name = "`reward_currency`")
    @Schema(name = "奖励币种")
    private String rewardCurrency;

    @Column(name = "`rewards`")
    @Schema(name = "奖励")
    private String rewards;

    @Schema(name = "奖励展示数量")
    @Column(name = "`reward_show_amount`")
    private String rewardShowAmount;

    @Column(name = "`publish_time`")
    @Schema(name = "计划发布时间")
    private Timestamp publishTime;

    @Column(name = "`connect_url`")
    @Schema(name = "任务授权链接")
    private String connectUrl;

    @Column(name = "attr")
    @Schema(name = "任务额外属性")
    private String attr;

    public void copy(TaskConfig source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
